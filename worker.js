/**
 * Cloudflare Worker 主入口文件
 * 文件中转服务 - 临时中转柜
 */

// 导入所有模块
import { Router } from './router.js';
import { handleUpload, handleFetch, handleDownload } from './handlers.js';
import { getHomePage, getStorePage, getRetrievePage, getErrorPage } from './templates.js';

// 创建路由实例
const router = new Router();

// 配置路由
router
  .get('/', () => new Response(getHomePage(), { headers: { 'Content-Type': 'text/html;charset=UTF-8' } }))
  .get('/store', () => new Response(getStorePage(), { headers: { 'Content-Type': 'text/html;charset=UTF-8' } }))
  .get('/retrieve', () => new Response(getRetrievePage(), { headers: { 'Content-Type': 'text/html;charset=UTF-8' } }))
  .post('/upload', handleUpload)
  .post('/fetch', handleFetch)
  .get('/download/:fileId', handleDownload);

/**
 * Cloudflare Worker 主导出对象
 * 处理所有传入的HTTP请求
 */
export default {
  async fetch(request, env, ctx) {
    try {
      return await router.handle(request, env, ctx);
    } catch (err) {
      console.error('Worker error:', err);
      return new Response(getErrorPage(`服务器发生错误: ${err.message}`), {
        status: 500,
        headers: { 'Content-Type': 'text/html;charset=UTF-8' }
      });
    }
  }
};