# 临时中转柜 - 文件中转服务

一个基于 Cloudflare Workers 的文件和信息中转服务，支持临时存储文件和文本消息。

## 项目结构

项目已经重构为模块化架构，便于维护和扩展：

```
filer/
├── worker.js          # 主入口文件，配置路由和导出Worker
├── router.js          # 路由系统，处理URL路径匹配
├── handlers.js        # 业务逻辑处理函数
├── utils.js           # 工具函数（密码哈希、文件大小格式化等）
├── templates.js       # HTML模板和样式
└── README.md          # 项目说明文档
```

## 模块说明

### 1. worker.js - 主入口文件
- 导入所有必要的模块
- 配置路由规则
- 导出 Cloudflare Worker 的主处理函数
- 统一错误处理

### 2. router.js - 路由系统
- `Router` 类：简单的路由匹配器
- 支持路径参数（如 `/download/:fileId`）
- 支持 GET 和 POST 方法
- 自动解析路径参数

### 3. handlers.js - 业务处理函数
- `handleUpload`: 处理文件和消息上传
- `handleFetch`: 处理内容提取请求
- `handleDownload`: 处理文件下载请求
- 包含完整的业务逻辑和KV存储操作

### 4. utils.js - 工具函数
- `hashPassword`: SHA-256密码哈希
- `formatBytes`: 文件大小格式化显示

### 5. templates.js - HTML模板
- `getHomePage`: 首页HTML
- `getStorePage`: 存储页面HTML（包含复杂的文件上传界面）
- `getRetrievePage`: 提取页面HTML
- `getSuccessPage`: 成功页面HTML
- `getErrorPage`: 错误页面HTML
- `getRetrieveResultPage`: 提取结果页面HTML
- 包含所有CSS样式和JavaScript代码

## 功能特性

- 📁 **文件上传**: 支持多文件上传，总大小限制25MB
- 💬 **文本消息**: 支持附加文本留言
- 🔐 **密码保护**: 使用密码保护内容安全
- ⏰ **自动过期**: 支持1小时到7天的自动过期时间
- 🔥 **阅后即焚**: 可选的一次性访问模式
- 📱 **二维码**: 自动生成取件二维码
- 🎨 **现代界面**: 响应式设计，支持拖拽上传

## 部署说明

1. 确保所有文件都在同一目录下
2. 在 Cloudflare Workers 中创建新的 Worker
3. 将 `worker.js` 的内容复制到 Worker 编辑器中
4. 配置 KV 命名空间绑定为 `filer`
5. 部署并测试

## 开发优势

模块化架构带来的优势：

- **可维护性**: 每个模块职责单一，易于理解和修改
- **可扩展性**: 新功能可以独立开发和测试
- **代码复用**: 工具函数和模板可以在不同地方复用
- **团队协作**: 不同开发者可以并行开发不同模块
- **调试友好**: 问题定位更加精确

## 注意事项

- 所有模块使用 ES6 模块语法（import/export）
- 确保 Cloudflare Workers 环境支持模块导入
- KV 存储需要正确配置绑定名称为 `filer`
