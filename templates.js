/**
 * HTML模板和样式模块
 * 包含所有页面生成函数和CSS样式
 */

import { formatBytes } from './utils.js';

// 应用名称
const AppName = "临时中转柜";

/**
 * 根据文件类型获取文件分类和图标
 * @param {string} mimeType - MIME类型
 * @param {string} fileName - 文件名
 * @returns {Object} - 包含类型、图标和是否可预览的信息
 */
const getFileTypeInfo = (mimeType, fileName) => {
  const ext = fileName.toLowerCase().split('.').pop() || '';

  if (mimeType.startsWith('image/')) {
    return { type: 'image', icon: 'IMG', canPreview: true };
  } else if (mimeType.startsWith('video/')) {
    return { type: 'video', icon: 'VID', canPreview: true };
  } else if (mimeType.startsWith('audio/')) {
    return { type: 'audio', icon: 'AUD', canPreview: true };
  } else if (mimeType === 'application/pdf') {
    return { type: 'pdf', icon: 'PDF', canPreview: true };
  } else if (mimeType.startsWith('text/') || ['txt', 'md', 'json', 'xml', 'csv'].includes(ext)) {
    return { type: 'document', icon: 'TXT', canPreview: false };
  } else if (['zip', 'rar', '7z', 'tar', 'gz'].includes(ext)) {
    return { type: 'archive', icon: 'ZIP', canPreview: false };
  } else {
    return { type: 'other', icon: 'FILE', canPreview: false };
  }
};

/**
 * 获取通用CSS样式
 * @returns {string} - CSS样式字符串
 */
const getCommonStyle = () => `
  <style>
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;700&display=swap');:root{--bg-color:#f7f8fa;--container-bg:#ffffff;--primary-color:#3182ce;--primary-hover:#2b6cb0;--secondary-color:#718096;--text-color:#2d3748;--label-color:#4a5568;--border-color:#e2e8f0;--border-focus:#3182ce;--success-bg:#f0fff4;--success-border:#9ae6b4;--error-bg:#fff5f5;--error-border:#feb2b2;--warning-bg:#fffaf0;--warning-border:#fbd38d}body{font-family:'Inter',sans-serif;margin:0;background-color:var(--bg-color);color:var(--text-color);display:flex;justify-content:center;align-items:center;min-height:100vh;padding:1rem}.container{background:var(--container-bg);padding:2rem 2.5rem;border-radius:12px;box-shadow:0 10px 25px -5px rgba(0,0,0,.05),0 4px 6px -2px rgba(0,0,0,.04);text-align:center;max-width:520px;width:100%;box-sizing:border-box}h1{color:var(--text-color);margin:0 0 .5rem 0;font-weight:700;font-size:1.875rem}h2{font-size:1.5rem;margin-bottom:1rem}p{color:var(--secondary-color);margin-top:0;line-height:1.6}.btn-group{margin-top:2rem;display:flex;gap:1rem;justify-content:center;flex-wrap:wrap}.btn{background-color:var(--primary-color);color:#fff;border:none;padding:14px 28px;border-radius:8px;font-size:1rem;font-weight:500;cursor:pointer;text-decoration:none;transition:all .2s ease-in-out;display:inline-flex;align-items:center;justify-content:center}.btn:hover{background-color:var(--primary-hover);transform:translateY(-2px);box-shadow:0 4px 12px rgba(0,0,0,.1)}.btn:disabled{background-color:#a0aec0;cursor:not-allowed;transform:none;box-shadow:none}.btn-secondary{background-color:#edf2f7;color:var(--text-color)}.btn-secondary:hover{background-color:#e2e8f0}.form-group{margin-bottom:1.5rem;text-align:left}label{display:block;margin-bottom:.5rem;font-weight:500;color:var(--label-color);font-size:.875rem}input[type=text],textarea{width:100%;padding:12px;border:1px solid var(--border-color);border-radius:8px;box-sizing:border-box;font-size:1rem;transition:border-color .2s,box-shadow .2s;background-color:white;}input[type=text]:focus,textarea:focus{outline:none;border-color:var(--border-focus);box-shadow:0 0 0 2px rgba(49,130,206,.2)}textarea{resize:vertical;min-height:90px}#drop-zone{border:2px dashed var(--border-color);border-radius:8px;padding:2.5rem 1rem;text-align:center;color:var(--secondary-color);cursor:pointer;transition:all .2s ease-in-out}#drop-zone.dragover{border-color:var(--primary-color);background-color:rgba(49,130,206,.05)}#drop-zone svg{width:48px;height:48px;margin-bottom:1rem;color:#cbd5e0}#file-list{margin-top:1rem;text-align:left}#progress-container{display:none;margin-top:1.5rem}.progress-bar{width:100%;background-color:#e2e8f0;border-radius:8px;overflow:hidden}.progress-bar-inner{height:12px;width:0;background-color:var(--primary-color);transition:width .2s ease;border-radius:8px}#progress-text{font-size:.875rem;color:var(--label-color);margin-top:.5rem;font-weight:500}.alert{padding:1.25rem;border-radius:8px;border:1px solid;margin:2rem 0;text-align:left}.alert p{margin:0}.alert-success{background-color:var(--success-bg);border-color:var(--success-border);color:#2f855a}.alert-error{background-color:var(--error-bg);border-color:var(--error-border);color:#c53030}.alert-warning{background-color:var(--warning-bg);border-color:var(--warning-border);color:#975a16}.password-display{display:flex;align-items:center;gap:1rem;background:#ebf4ff;padding:1rem;border-radius:6px;font-size:1.25rem;font-weight:700;letter-spacing:2px;text-align:center;margin:1rem 0;color:var(--primary-color);border:1px dashed var(--primary-color)}.password-display span{user-select:all;flex-grow:1;text-align:center}.btn-copy{padding:6px 12px;font-size:0.8rem;font-weight:normal;flex-shrink:0;}.result-box{text-align:left;margin-top:2rem;background-color:#f7fafc;border:1px solid #e2e8f0;border-radius:8px;padding:1.5rem}.result-box .message{font-size:1.1rem;line-height:1.7;word-wrap:break-word;white-space:pre-wrap;color:var(--text-color)}.file-download-list a{display:flex;align-items:center;gap:1rem;padding:.75rem 1rem;border-radius:8px;text-decoration:none;color:var(--text-color);transition:background-color .2s;margin-bottom:.5rem;border:1px solid var(--border-color)}.file-download-list a:hover{background-color:#edf2f7}.file-download-list .file-info{flex-grow:1;min-width:0}.file-download-list .file-name{font-weight:500;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.file-download-list .file-size{font-size:.8rem;color:var(--secondary-color)}.file-download-list svg{flex-shrink:0;width:24px;height:24px;color:var(--primary-color)}.option-group{display:flex;align-items:center;gap:.5rem;margin-top:-.5rem;margin-bottom:1.5rem;text-align:left}.option-group label{margin-bottom:0;font-weight:400}.input-group{position:relative;display:flex;align-items:stretch;width:100%}.input-group input{flex:1 1 auto;width:1%;min-width:0;border-top-right-radius:0;border-bottom-right-radius:0}.input-group-btn{display:flex;align-items:center;justify-content:center;flex-shrink:0;width:48px;background-color:#fff;border:1px solid var(--border-color);border-left:0;padding:0;border-top-right-radius:8px;border-bottom-right-radius:8px;cursor:pointer;color:var(--secondary-color);transition:all .2s}.input-group-btn:hover{color:var(--primary-color);background-color:#f7f8fa}.input-group-btn svg{width:22px;height:22px}.input-group input:focus+.input-group-btn{border-color:var(--border-focus);box-shadow:0 0 0 2px rgba(49,130,206,.2);border-left:0}.select-wrapper{position:relative;width:100%}.select-wrapper::after{content:'';position:absolute;right:12px;top:50%;transform:translateY(-50%);width:20px;height:20px;background-image:url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20' fill='%23718096'%3E%3Cpath fill-rule='evenodd' d='M5.23 7.21a.75.75 0 011.06.02L10 11.168l3.71-3.938a.75.75 0 111.08 1.04l-4.25 4.5a.75.75 0 01-1.08 0l-4.25-4.5a.75.75 0 01.02-1.06z' clip-rule='evenodd' /%3E%3C/svg%3E");background-repeat:no-repeat;background-position:center;pointer-events:none;transition:color .2s}.select-wrapper select{-webkit-appearance:none;-moz-appearance:none;appearance:none;width:100%;padding:12px;padding-right:40px;border:1px solid var(--border-color);border-radius:8px;box-sizing:border-box;font-size:1rem;background-color:white;transition:border-color .2s,box-shadow .2s;cursor:pointer}.select-wrapper select:focus{outline:none;border-color:var(--border-focus);box-shadow:0 0 0 2px rgba(49,130,206,.2)}
    
    /* --- 新增和修改的样式 --- */
    .file-item{background-color:#f7fafc;border:1px solid var(--border-color);padding:.5rem 1rem;border-radius:6px;font-size:.875rem;margin-bottom:.5rem;display:flex;justify-content:space-between;align-items:center;gap:.75rem;}
    .file-item-name{flex-grow:1;min-width:0;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;font-weight:500;}
    .file-item span:not(.file-item-name){color:var(--secondary-color);flex-shrink:0;}
    .file-item-delete{background:none;border:none;padding:0;cursor:pointer;color:var(--secondary-color);flex-shrink:0;line-height:1;transition:color .2s;}
    .file-item-delete:hover{color:#a72121;}
    .file-item-delete svg{width:18px;height:18px;display:block;}

    /* --- 预览功能样式 --- */
    .file-preview-item{display:flex;align-items:center;gap:1rem;padding:.75rem 1rem;border-radius:8px;margin-bottom:.5rem;border:1px solid var(--border-color);background-color:#f7fafc;}
    .file-preview-item .file-icon{flex-shrink:0;width:32px;height:32px;display:flex;align-items:center;justify-content:center;border-radius:6px;background-color:var(--primary-color);color:white;font-size:.75rem;font-weight:600;}
    .file-preview-item .file-info{flex-grow:1;min-width:0;}
    .file-preview-item .file-name{font-weight:500;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;margin-bottom:.25rem;}
    .file-preview-item .file-size{font-size:.8rem;color:var(--secondary-color);}
    .file-preview-item .file-actions{display:flex;gap:.5rem;flex-shrink:0;}
    .file-preview-item .btn-preview,.file-preview-item .btn-download{padding:.375rem .75rem;font-size:.8rem;border-radius:6px;text-decoration:none;transition:all .2s;display:inline-flex;align-items:center;gap:.375rem;}
    .file-preview-item .btn-preview{background-color:var(--primary-color);color:white;}
    .file-preview-item .btn-preview:hover{background-color:var(--primary-hover);}
    .file-preview-item .btn-download{background-color:#edf2f7;color:var(--text-color);}
    .file-preview-item .btn-download:hover{background-color:#e2e8f0;}
    .file-preview-item .btn-preview svg,.file-preview-item .btn-download svg{width:14px;height:14px;}

    /* 预览模态框样式 */
    .preview-modal{position:fixed;top:0;left:0;width:100%;height:100%;background-color:rgba(0,0,0,0.9);display:none;z-index:1000;align-items:center;justify-content:center;}
    .preview-modal.show{display:flex;}
    .preview-modal-content{position:relative;max-width:90vw;max-height:90vh;background:white;border-radius:12px;overflow:hidden;box-shadow:0 25px 50px -12px rgba(0,0,0,0.25);}
    .preview-modal-header{display:flex;justify-content:space-between;align-items:center;padding:1rem 1.5rem;border-bottom:1px solid var(--border-color);background-color:#f8f9fa;}
    .preview-modal-title{font-weight:600;color:var(--text-color);margin:0;font-size:1rem;}
    .preview-modal-close{background:none;border:none;font-size:1.5rem;cursor:pointer;color:var(--secondary-color);padding:0;width:32px;height:32px;display:flex;align-items:center;justify-content:center;border-radius:6px;transition:all .2s;}
    .preview-modal-close:hover{background-color:#e2e8f0;color:var(--text-color);}
    .preview-modal-body{padding:0;display:flex;align-items:center;justify-content:center;min-height:300px;max-height:70vh;overflow:auto;}
    .preview-modal-body img{max-width:100%;max-height:100%;object-fit:contain;}
    .preview-modal-body video{max-width:100%;max-height:100%;outline:none;}
    .preview-modal-body audio{width:100%;max-width:400px;margin:2rem;}
    .preview-modal-body iframe{width:100%;height:70vh;border:none;}
    .preview-modal-body .preview-error{padding:2rem;text-align:center;color:var(--secondary-color);}

    /* 文件类型图标样式 */
    .file-icon.image{background-color:#10b981;}
    .file-icon.video{background-color:#8b5cf6;}
    .file-icon.audio{background-color:#f59e0b;}
    .file-icon.pdf{background-color:#ef4444;}
    .file-icon.document{background-color:#6b7280;}
    .file-icon.archive{background-color:#0ea5e9;}
    .file-icon.other{background-color:var(--secondary-color);}

    /* 打包下载按钮样式 */
    .package-download-section{margin-top:1.5rem;padding-top:1rem;border-top:1px solid var(--border-color);text-align:center;}
    .package-download-btn{background-color:#059669;color:white;display:inline-flex;align-items:center;justify-content:center;gap:.5rem;width:100%;padding:12px 20px;font-weight:600;border-radius:8px;transition:all .2s;box-shadow:0 2px 4px rgba(5,150,105,0.2);}
    .package-download-btn:hover{background-color:#047857;color:white;transform:translateY(-1px);box-shadow:0 4px 8px rgba(5,150,105,0.3);}
    .package-download-btn svg{width:18px;height:18px;flex-shrink:0;}
  </style>
`;

/**
 * 页面包装器
 * @param {string} title - 页面标题
 * @param {string} content - 页面内容
 * @returns {string} - 完整的HTML页面
 */
const getPageWrapper = (title, content) => `<!DOCTYPE html><html lang="zh-CN"><head><meta charset="UTF-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><title>${title} - ${AppName}</title>${getCommonStyle()}</head><body><div class="container">${content}</div></body></html>`;

/**
 * 获取首页HTML
 * @returns {string} - 首页HTML
 */
export const getHomePage = () => getPageWrapper('主页', `<h1>${AppName}</h1><p>文件与信息中转站。</p><div class="btn-group"><a href="/store" class="btn">存入内容</a><a href="/retrieve" class="btn btn-secondary">提取内容</a></div>`);

/**
 * 获取提取页面HTML
 * @returns {string} - 提取页面HTML
 */
export const getRetrievePage = () => getPageWrapper('提取内容', `<h2 style="margin-top:0;">提取内容</h2><p>输入取件密码以获取您的文件和信息。</p><form id="retrieve-form"><div class="form-group"><label for="password">取件密码</label><input type="text" id="password" name="password" required autocomplete="off"></div><div class="btn-group"><button type="submit" class="btn">立即提取</button><a href="/" class="btn btn-secondary">返回</a></div></form><script>document.addEventListener('DOMContentLoaded',()=>{if(window.location.hash){const e=decodeURIComponent(window.location.hash.substring(1));document.getElementById('password').value=e}});document.getElementById('retrieve-form').addEventListener('submit',async e=>{e.preventDefault();const t=e.target,n=t.querySelector('button[type="submit"]');n.disabled=!0,n.textContent='提取中...';const o=new FormData(t);document.body.innerHTML=await(await fetch('/fetch',{method:'POST',body:o})).text()});</script>`);

/**
 * 获取成功页面HTML
 * @param {string} password - 取件密码
 * @param {string|null} qrCodeDataUrl - 二维码数据URL
 * @returns {string} - 成功页面HTML
 */
export const getSuccessPage = (password, qrCodeDataUrl) => {
  const qrCodeHtml = qrCodeDataUrl
    ? `<p style="margin-top:1.5rem;">您也可以使用手机扫描下方二维码快速取件：</p><div style="margin: 1rem 0; text-align: center;"><img src="${qrCodeDataUrl}" alt="取件二维码" style="border: 1px solid var(--border-color); border-radius: 8px; padding: 5px;"></div>`
    : '';

  const message = `
    <p>上传成功！您的取件密码是:</p>
    <div class="password-display">
      <span>${password}</span>
      <button type="button" class="btn btn-secondary btn-copy" onclick="navigator.clipboard.writeText('${password}'); this.textContent='已复制!'; setTimeout(() => this.textContent='复制', 2000)">复制</button>
    </div>
    ${qrCodeHtml}
    <p style="font-size: 0.875rem; color: var(--secondary-color);">内容将在指定时间后或首次提取后（如已勾选）销毁。</p>
  `;
  return getPageWrapper('操作成功', `<h2>操作成功</h2><div class="alert alert-success" style="text-align:center;">${message}</div><div class="btn-group"><a href="/" class="btn">返回主页</a></div>`);
};

/**
 * 获取错误页面HTML
 * @param {string} message - 错误消息
 * @returns {string} - 错误页面HTML
 */
export const getErrorPage = (message) => getPageWrapper('操作失败', `<h2 style="color:#c53030;">操作失败</h2><div class="alert alert-error"><p>${message}</p></div><div class="btn-group"><a href="javascript:history.back()" class="btn">返回重试</a><a href="/" class="btn btn-secondary">返回主页</a></div>`);

/**
 * 获取存储页面HTML
 * @returns {string} - 存储页面HTML
 */
export const getStorePage = () => {
  const content = `
    <h2 style="margin-top:0;">存入内容</h2>
    <p>输入密码、留言并上传文件。内容将在指定时间后自动销毁。</p>
    <form id="upload-form">
        <div class="form-group">
            <label for="password">取件密码 (必填)</label>
            <div class="input-group">
                <input type="text" id="password" name="password" required autocomplete="off" placeholder="输入或点击右侧生成">
                <button type="button" id="generate-pw-btn" class="input-group-btn" title="生成随机密码">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                      <path d="M14 3.1157C13.4228 2.42841 12.5772 2 11.6667 2C10.7561 2 9.91054 2.42841 9.33333 3.1157L6.66667 6.33333C6.24816 6.84074 6 7.4721 6 8.125C6 9.54924 7.20076 10.75 8.625 10.75H14.7083C16.1326 10.75 17.3333 9.54924 17.3333 8.125C17.3333 7.4721 17.0852 6.84074 16.6667 6.33333L14 3.1157Z" opacity="0.4"/>
                      <path fill-rule="evenodd" clip-rule="evenodd" d="M5.5 11.5C4.67157 11.5 4 12.1716 4 13V18C4 18.8284 4.67157 19.5 5.5 19.5H18.5C19.3284 19.5 20 18.8284 20 18V13C20 12.1716 19.3284 11.5 18.5 11.5H5.5ZM13.25 16C13.25 16.4142 12.9142 16.75 12.5 16.75C12.0858 16.75 11.75 16.4142 11.75 16C11.75 15.5858 12.0858 15.25 12.5 15.25C12.9142 15.25 13.25 15.5858 13.25 16ZM16.25 14C16.25 14.4142 15.9142 14.75 15.5 14.75C15.0858 14.75 14.75 14.4142 14.75 14C14.75 13.5858 15.0858 13.25 15.5 13.25C15.9142 13.25 16.25 13.5858 16.25 14ZM9.25 14C9.25 14.4142 8.91421 14.75 8.5 14.75C8.08579 14.75 7.75 14.4142 7.75 14C7.75 13.5858 8.08579 13.25 8.5 13.25C8.91421 13.25 9.25 13.5858 9.25 14Z"/>
                    </svg>
                </button>
            </div>
        </div>
        <div class="form-group"><label for="message">附加留言 (可选)</label><textarea id="message" name="message"></textarea></div>
        <div class="form-group"><label>上传文件 (可选, 支持多个, 总计上限25MB)</label><div id="drop-zone"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" /></svg><div>将文件拖到此处，或<a href="#" onclick="document.getElementById('file-input').click(); return false;">点击选择</a></div><input type="file" id="file-input" name="file" multiple style="display: none;"></div><div id="file-list"></div></div>

        <div class="form-group">
            <label for="ttl">内容有效期</label>
            <div class="select-wrapper">
                <select id="ttl" name="ttl">
                    <option value="3600">1 小时</option>
                    <option value="21600">6 小时</option>
                    <option value="86400" selected>1 天 (默认)</option>
                    <option value="604800">7 天</option>
                </select>
            </div>
        </div>

        <div class="option-group">
          <input type="checkbox" id="onetime" name="onetime">
          <label for="onetime">首次提取后立即销毁 (阅后即焚)</label>
        </div>

        <div id="progress-container"><div class="progress-bar"><div id="progress-bar-inner" class="progress-bar-inner"></div></div><div id="progress-text"></div></div>
        <div class="btn-group"><button type="submit" class="btn" id="submit-btn">确认上传</button><a href="/" class="btn btn-secondary">返回</a></div>
    </form>
    <script>
      document.addEventListener('DOMContentLoaded', () => {
        const dropZone = document.getElementById('drop-zone');
        const fileInput = document.getElementById('file-input');
        const fileListDisplay = document.getElementById('file-list');
        const form = document.getElementById('upload-form');
        const submitBtn = document.getElementById('submit-btn');
        const progressContainer = document.getElementById('progress-container');
        const progressBarInner = document.getElementById('progress-bar-inner');
        const progressText = document.getElementById('progress-text');
        const passwordInput = document.getElementById('password');
        const generatePwBtn = document.getElementById('generate-pw-btn');

        let selectedFiles = [];

        function generateAndSetPassword() {
          const adjectives = ["agile","brave","calm","dark","eager","fast","gold","happy","icy","jolly","kind","lucky","magic","noble","ocean","proud","quick","red","sunny","tiny","urban","vast","wild","young","zesty"];
          const nouns = ["ant","bear","cat","dog","eel","fox","goat","hawk","ibis","jay","koala","lark","mole","newt","owl","puma","quail","rat","seal","toad","urchin","viper","wolf","yak","zebra"];
          const number = Math.floor(10 + Math.random() * 90);
          passwordInput.value = \`\${adjectives[Math.floor(Math.random() * adjectives.length)]}-\${nouns[Math.floor(Math.random() * nouns.length)]}-\${number}\`;
        }
        generatePwBtn.addEventListener("click", generateAndSetPassword);

        function formatBytes(bytes, decimals = 2) {
          if (bytes === 0) return "0 Bytes";
          const k = 1024;
          const dm = decimals < 0 ? 0 : decimals;
          const sizes = ["Bytes", "KB", "MB", "GB", "TB"];
          const i = Math.floor(Math.log(bytes) / Math.log(k));
          return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + " " + sizes[i];
        }

        function updateFileList() {
          fileListDisplay.innerHTML = "";
          if (selectedFiles.length > 0) {
            selectedFiles.forEach((file, index) => {
              const fileItem = document.createElement("div");
              fileItem.className = "file-item";
              fileItem.innerHTML = \`
                <span class="file-item-name" title="\${file.name}">\${file.name}</span>
                <span>\${formatBytes(file.size)}</span>
                <button type="button" class="file-item-delete" data-index="\${index}" title="删除文件">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                      <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.28 7.22a.75.75 0 00-1.06 1.06L8.94 10l-1.72 1.72a.75.75 0 101.06 1.06L10 11.06l1.72 1.72a.75.75 0 101.06-1.06L11.06 10l1.72-1.72a.75.75 0 00-1.06-1.06L10 8.94 8.28 7.22z" clip-rule="evenodd" />
                    </svg>
                </button>
              \`;
              fileListDisplay.appendChild(fileItem);
            });
          }
        }

        fileListDisplay.addEventListener('click', (event) => {
          const deleteButton = event.target.closest('.file-item-delete');
          if (deleteButton) {
            const indexToRemove = parseInt(deleteButton.dataset.index, 10);
            if (!isNaN(indexToRemove)) {
              selectedFiles.splice(indexToRemove, 1);
              updateFileList();
            }
          }
        });

        function handleFiles(files) {
          selectedFiles = [...selectedFiles, ...Array.from(files)];
          fileInput.value = "";
          updateFileList();
        }

        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
          dropZone.addEventListener(eventName, e => { e.preventDefault(); e.stopPropagation(); });
        });
        ['dragenter', 'dragover'].forEach(eventName => {
          dropZone.addEventListener(eventName, () => dropZone.classList.add('dragover'));
        });
        ['dragleave', 'drop'].forEach(eventName => {
          dropZone.addEventListener(eventName, () => dropZone.classList.remove('dragover'));
        });

        dropZone.addEventListener('click', () => fileInput.click());
        dropZone.addEventListener('drop', e => handleFiles(e.dataTransfer.files));
        fileInput.addEventListener('change', e => handleFiles(e.target.files));

        form.addEventListener('submit', (e) => {
          e.preventDefault();
          const password = form.password.value;
          const message = form.message.value;
          if (!password || (!message && selectedFiles.length === 0)) {
            alert("密码为必填项，且留言和文件至少需要提供一个。");
            return;
          }
          const totalSize = selectedFiles.reduce((sum, file) => sum + file.size, 0);
          if (totalSize > 25 * 1024 * 1024) {
            alert("总文件大小超过25MB限制！");
            return;
          }

          submitBtn.disabled = true;
          submitBtn.textContent = '上传中...';
          progressContainer.style.display = 'block';

          const formData = new FormData(form);
          formData.delete('file');
          selectedFiles.forEach(file => {
            formData.append('file', file);
          });

          const xhr = new XMLHttpRequest();
          xhr.open('POST', '/upload', true);
          xhr.upload.onprogress = (event) => {
            if (event.lengthComputable) {
              const percentComplete = (event.loaded / event.total) * 100;
              progressBarInner.style.width = percentComplete + '%';
              progressText.textContent = \`上传中... \${Math.round(percentComplete)}%\`;
            }
          };
          xhr.onload = () => {
            if (xhr.status >= 200 && xhr.status < 300) {
              document.body.innerHTML = xhr.responseText;
            } else {
              alert(\`上传失败: \${xhr.statusText || '未知错误'}\`);
              submitBtn.disabled = false;
              submitBtn.textContent = '确认上传';
              progressContainer.style.display = 'none';
            }
          };
          xhr.onerror = () => {
            alert('上传时发生网络错误。');
            submitBtn.disabled = false;
            submitBtn.textContent = '确认上传';
            progressContainer.style.display = 'none';
          };
          xhr.send(formData);
        });
      });
    </script>
  `;
  return getPageWrapper('存入内容', content);
};

/**
 * 获取提取结果页面HTML
 * @param {Object} metadata - 元数据对象，包含消息和文件信息
 * @param {string} passwordHash - 密码哈希，用于打包下载
 * @returns {string} - 提取结果页面HTML
 */
export const getRetrieveResultPage = (metadata, passwordHash) => {
  let filesHtml = '<p>没有附加文件。</p>';
  // 重构文件显示逻辑，添加预览功能
  if (metadata.files && metadata.files.length > 0) {
    filesHtml = metadata.files.map(file => {
      const fileInfo = getFileTypeInfo(file.type, file.name);
      const previewButton = fileInfo.canPreview ? `
        <button class="btn-preview" onclick="openPreview('${file.id}', ${JSON.stringify(file.name)}, ${JSON.stringify(file.type)})">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
            <path d="M10 12.5a2.5 2.5 0 100-5 2.5 2.5 0 000 5z" />
            <path fill-rule="evenodd" d="M.664 10.59a1.651 1.651 0 010-1.186A11.8 11.8 0 0110 2c4.257 0 7.893 2.66 9.336 6.41.147.381.147.804 0 1.186A11.8 11.8 0 0110 18c-4.257 0-7.893-2.66-9.336-6.41zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd" />
          </svg>
          预览
        </button>
      ` : '';

      return `
        <div class="file-preview-item">
          <div class="file-icon ${fileInfo.type}">${fileInfo.icon}</div>
          <div class="file-info">
            <div class="file-name">${file.name}</div>
            <div class="file-size">${formatBytes(file.size)}</div>
          </div>
          <div class="file-actions">
            ${previewButton}
            <a href="/download/${file.id}" download="${file.name}" class="btn-download">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path d="M10.75 2.75a.75.75 0 00-1.5 0v8.614L6.295 8.235a.75.75 0 10-1.09 1.03l4.25 4.5a.75.75 0 001.09 0l4.25-4.5a.75.75 0 00-1.09-1.03l-2.955 3.129V2.75z" />
                <path d="M3.5 12.75a.75.75 0 00-1.5 0v2.5A2.75 2.75 0 004.75 18h10.5A2.75 2.75 0 0018 15.25v-2.5a.75.75 0 00-1.5 0v2.5c0 .69-.56 1.25-1.25 1.25H4.75c-.69 0-1.25-.56-1.25-1.25v-2.5z" />
              </svg>
              下载
            </a>
          </div>
        </div>
      `;
    }).join('');
  }

  const warningMessage = metadata.onetime
    ? `<p><strong>注意：</strong>此内容为"阅后即焚"模式，所有数据已被彻底销毁。此页面是您查看的唯一机会。</p>`
    : `<p><strong>注意：</strong>此内容将在 <strong>5 分钟</strong>后彻底销毁，请尽快保存。</p>`;

  // 如果有文件，显示打包下载按钮
  const packageDownloadButton = (metadata.files && metadata.files.length > 0) ? `
    <div class="package-download-section">
      <a href="/package/${passwordHash}" class="btn package-download-btn" download>
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
          <path fill-rule="evenodd" d="M2 3a1 1 0 00-1 1v1a1 1 0 001 1h16a1 1 0 001-1V4a1 1 0 00-1-1H2zm0 4.5h16l-.811 7.71a2 2 0 01-1.99 1.79H4.8a2 2 0 01-1.99-1.79L2 7.5zM10 9a.75.75 0 01.75.75v2.546l.943-1.048a.75.75 0 111.014 1.104l-2.25 2.5a.75.75 0 01-1.114 0l-2.25-2.5a.75.75 0 111.014-1.104l.943 1.048V9.75A.75.75 0 0110 9z" clip-rule="evenodd" />
        </svg>
        ${metadata.files.length === 1 ? '下载文件' : `打包下载全部 (${metadata.files.length} 个文件)`}
      </a>
    </div>
  ` : '';

  const content = `
    <h2>提取成功</h2>
    <div class="alert alert-warning">${warningMessage}</div>
    <div class="result-box"><label>留言内容:</label><p class="message">${metadata.message || '(无留言内容)'}</p></div>
    <div class="form-group" style="margin-top: 2rem;">
      <label>附加文件:</label>
      <div class="file-preview-list">${filesHtml}</div>
      ${packageDownloadButton}
    </div>
    <div class="btn-group"><a href="/" class="btn btn-secondary">返回主页</a></div>

    <!-- 预览模态框 -->
    <div id="preview-modal" class="preview-modal">
      <div class="preview-modal-content">
        <div class="preview-modal-header">
          <h3 class="preview-modal-title" id="preview-title">文件预览</h3>
          <button class="preview-modal-close" onclick="closePreview()">×</button>
        </div>
        <div class="preview-modal-body" id="preview-body">
          <div class="preview-error">加载中...</div>
        </div>
      </div>
    </div>

    <script>
      // 预览功能JavaScript
      function openPreview(fileId, fileName, mimeType) {
        const modal = document.getElementById('preview-modal');
        const title = document.getElementById('preview-title');
        const body = document.getElementById('preview-body');

        title.textContent = fileName;
        body.innerHTML = '<div class="preview-error">加载中...</div>';
        modal.classList.add('show');

        const previewUrl = '/preview/' + fileId;

        if (mimeType.startsWith('image/')) {
          body.innerHTML = '<img src="' + previewUrl + '" alt="' + fileName + '" onload="this.style.opacity=1" style="opacity:0;transition:opacity 0.3s" onerror="showPreviewError()">';
        } else if (mimeType.startsWith('video/')) {
          body.innerHTML = '<video controls preload="metadata" onloadedmetadata="this.style.opacity=1" style="opacity:0;transition:opacity 0.3s" onerror="showPreviewError()"><source src="' + previewUrl + '" type="' + mimeType + '">您的浏览器不支持视频播放。</video>';
        } else if (mimeType.startsWith('audio/')) {
          body.innerHTML = '<audio controls preload="metadata" onloadedmetadata="this.style.opacity=1" style="opacity:0;transition:opacity 0.3s" onerror="showPreviewError()"><source src="' + previewUrl + '" type="' + mimeType + '">您的浏览器不支持音频播放。</audio>';
        } else if (mimeType === 'application/pdf') {
          body.innerHTML = '<iframe src="' + previewUrl + '" onload="this.style.opacity=1" style="opacity:0;transition:opacity 0.3s" onerror="showPreviewError()">您的浏览器不支持PDF预览。</iframe>';
        } else {
          showPreviewError();
        }
      }

      function closePreview() {
        const modal = document.getElementById('preview-modal');
        modal.classList.remove('show');
        // 清理媒体元素以停止播放
        setTimeout(() => {
          const body = document.getElementById('preview-body');
          body.innerHTML = '<div class="preview-error">加载中...</div>';
        }, 300);
      }

      function showPreviewError() {
        const body = document.getElementById('preview-body');
        body.innerHTML = '<div class="preview-error">无法预览此文件类型或文件加载失败。</div>';
      }

      // 点击模态框背景关闭预览
      document.addEventListener('click', function(e) {
        const modal = document.getElementById('preview-modal');
        if (e.target === modal) {
          closePreview();
        }
      });

      // ESC键关闭预览
      document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
          closePreview();
        }
      });
    </script>`;
  return getPageWrapper('提取结果', content);
};
