/**
 * 简单的路由类，用于根据请求方法和路径分发请求。
 * 支持路径参数，例如 /download/:fileId
 */
export class Router {
  constructor() {
    this.routes = [];
  }

  pathToRegExp(path) {
    const pattern = path.replace(/:(\w+)/g, '(?<$1>[^/]+)');
    return new RegExp(`^${pattern}$`);
  }

  add(method, path, handler) {
    this.routes.push({
      method: method.toUpperCase(),
      path: this.pathToRegExp(path),
      handler,
    });
    return this;
  }

  get(path, handler) { return this.add('GET', path, handler); }
  post(path, handler) { return this.add('POST', path, handler); }

  async handle(request, ...args) {
    const { pathname } = new URL(request.url);
    const method = request.method;

    for (const route of this.routes) {
      if (route.method !== method) continue;
      const match = pathname.match(route.path);
      if (match) {
        const params = match.groups || {};
        return route.handler(request, ...args, params);
      }
    }

    // 如果没有匹配的路由，返回404
    return new Response('Not Found', { status: 404 });
  }
}
