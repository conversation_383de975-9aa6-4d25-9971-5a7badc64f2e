/**
 * 路由处理函数模块
 * 包含上传、获取、下载等核心业务逻辑
 */

import { hashPassword } from './utils.js';
import { getSuccessPage, getErrorPage, getRetrieveResultPage } from './templates.js';

/**
 * 处理文件上传请求
 * @param {Request} request - HTTP请求对象
 * @param {Object} env - 环境变量，包含KV存储
 * @returns {Promise<Response>} - HTTP响应
 */
export async function handleUpload(request, env) {
  const filerKV = env.filer;
  const formData = await request.formData();
  const password = formData.get('password');
  const message = formData.get('message');
  const onetime = formData.get('onetime') === 'true';
  const files = formData.getAll('file').filter(f => f.size > 0);
  const ttlInput = parseInt(formData.get('ttl'), 10);
  const allowedTtls = [3600, 21600, 86400, 604800];
  const expirationTtl = allowedTtls.includes(ttlInput) ? ttlInput : 86400;

  if (!password || (!message && files.length === 0)) {
    return new Response('密码为必填项，且留言和文件至少需要提供一个。', { status: 400 });
  }

  const totalSize = files.reduce((acc, file) => acc + file.size, 0);
  if (totalSize > 25 * 1024 * 1024) {
    return new Response('所有文件总大小不能超过 25MB。', { status: 400 });
  }

  const key = await hashPassword(password);
  
  const filesMetadata = [];
  const putOperations = [];

  for (const file of files) {
    const fileId = crypto.randomUUID();
    filesMetadata.push({ id: fileId, name: file.name, type: file.type, size: file.size });
    putOperations.push(
      filerKV.put(`file:${fileId}`, await file.arrayBuffer(), {
        expirationTtl: expirationTtl,
        metadata: { filename: file.name, contentType: file.type },
      })
    );
  }

  const mainMetadata = {
    message: message || '',
    files: filesMetadata,
    uploadTime: new Date().toISOString(),
    onetime: onetime
  };
  putOperations.push(filerKV.put(`meta:${key}`, JSON.stringify(mainMetadata), { expirationTtl: expirationTtl }));
  await Promise.all(putOperations);

  const { origin } = new URL(request.url);
  const retrievalUrl = `${origin}/retrieve#${encodeURIComponent(password)}`;

  let qrCodeDataUrl = null;
  try {
    const qrApiUrl = `https://api.qrserver.com/v1/create-qr-code/?size=180x180&data=${encodeURIComponent(retrievalUrl)}`;
    const qrResponse = await fetch(qrApiUrl);
    if (qrResponse.ok) {
      const contentType = qrResponse.headers.get('content-type') || 'image/png';
      const imageBuffer = await qrResponse.arrayBuffer();
      const base64String = btoa(String.fromCharCode(...new Uint8Array(imageBuffer)));
      qrCodeDataUrl = `data:${contentType};base64,${base64String}`;
    }
  } catch (e) { console.error("QR Code generation failed:", e); }
  
  return new Response(getSuccessPage(password, qrCodeDataUrl), { headers: { 'Content-Type': 'text/html;charset=UTF-8' } });
}

/**
 * 处理内容获取请求
 * @param {Request} request - HTTP请求对象
 * @param {Object} env - 环境变量，包含KV存储
 * @param {Object} ctx - 执行上下文
 * @returns {Promise<Response>} - HTTP响应
 */
export async function handleFetch(request, env, ctx) {
  const filerKV = env.filer;
  const formData = await request.formData();
  const password = formData.get('password');
  if (!password) return new Response('请输入密码。', { status: 400 });

  const key = await hashPassword(password);
  const meta_key = `meta:${key}`;
  const metadataStr = await filerKV.get(meta_key);

  if (!metadataStr) {
    return new Response(getErrorPage('未找到匹配项。请检查您的密码是否正确，或内容是否已过期。'), {
      status: 404, headers: { 'Content-Type': 'text/html;charset=UTF-8' }
    });
  }

  const metadata = JSON.parse(metadataStr);

  if (metadata.onetime) {
    const deleteOperations = [filerKV.delete(meta_key)];
    if (metadata.files && metadata.files.length > 0) {
      for (const fileInfo of metadata.files) {
        deleteOperations.push(filerKV.delete(`file:${fileInfo.id}`));
      }
    }
    ctx.waitUntil(Promise.all(deleteOperations));
  } else {
    const ttl_5_minutes = 300;
    const updateOperations = [filerKV.put(meta_key, metadataStr, { expirationTtl: ttl_5_minutes })];

    if (metadata.files && metadata.files.length > 0) {
      for (const fileInfo of metadata.files) {
        const file_key = `file:${fileInfo.id}`;
        const fileData = await filerKV.getWithMetadata(file_key, 'arrayBuffer');
        if (fileData.value) {
          updateOperations.push(
            filerKV.put(file_key, fileData.value, { expirationTtl: ttl_5_minutes, metadata: fileData.metadata })
          );
        }
      }
    }
    await Promise.all(updateOperations);
  }

  return new Response(getRetrieveResultPage(metadata), { headers: { 'Content-Type': 'text/html;charset=UTF-8' } });
}

/**
 * 处理文件下载请求
 * @param {Request} request - HTTP请求对象（未使用但保持接口一致性）
 * @param {Object} env - 环境变量，包含KV存储
 * @param {Object} ctx - 执行上下文（未使用但保持接口一致性）
 * @param {Object} params - 路由参数，包含fileId
 * @returns {Promise<Response>} - HTTP响应
 */
export async function handleDownload(request, env, ctx, params) {
  const { fileId } = params;
  const filerKV = env.filer;
  if (!fileId) return new Response('无效的下载链接', { status: 400 });
  const { value, metadata } = await filerKV.getWithMetadata(`file:${fileId}`, 'arrayBuffer');
  if (!value || !metadata) return new Response('文件未找到或已过期。', { status: 404 });
  return new Response(value, {
    headers: {
      'Content-Type': metadata.contentType || 'application/octet-stream',
      'Content-Disposition': `attachment; filename*=UTF-8''${encodeURIComponent(metadata.filename || 'download')}`,
    }
  });
}

/**
 * 处理文件预览请求
 * @param {Request} request - HTTP请求对象
 * @param {Object} env - 环境变量，包含KV存储
 * @param {Object} ctx - 执行上下文
 * @param {Object} params - 路由参数，包含fileId
 * @returns {Promise<Response>} - HTTP响应
 */
export async function handlePreview(request, env, ctx, params) {
  const { fileId } = params;
  const filerKV = env.filer;
  if (!fileId) return new Response('无效的预览链接', { status: 400 });

  const { value, metadata } = await filerKV.getWithMetadata(`file:${fileId}`, 'arrayBuffer');
  if (!value || !metadata) return new Response('文件未找到或已过期。', { status: 404 });

  const contentType = metadata.contentType || 'application/octet-stream';

  // 设置适当的缓存头和内容类型，用于在线预览
  return new Response(value, {
    headers: {
      'Content-Type': contentType,
      'Cache-Control': 'public, max-age=300', // 5分钟缓存
      'Content-Disposition': `inline; filename*=UTF-8''${encodeURIComponent(metadata.filename || 'preview')}`,
    }
  });
}
