/**
 * 路由处理函数模块
 * 包含上传、获取、下载等核心业务逻辑
 */

import { hashPassword } from './utils.js';
import { getSuccessPage, getErrorPage, getRetrieveResultPage } from './templates.js';

/**
 * 简单的ZIP文件创建器
 * 基于ZIP文件格式规范实现的轻量级ZIP创建功能
 */
class SimpleZip {
  constructor() {
    this.files = [];
  }

  addFile(filename, data) {
    this.files.push({
      filename: filename,
      data: new Uint8Array(data),
      crc32: this.calculateCRC32(new Uint8Array(data))
    });
  }

  calculateCRC32(data) {
    const crcTable = [];
    for (let i = 0; i < 256; i++) {
      let crc = i;
      for (let j = 0; j < 8; j++) {
        crc = (crc & 1) ? (0xEDB88320 ^ (crc >>> 1)) : (crc >>> 1);
      }
      crcTable[i] = crc;
    }

    let crc = 0xFFFFFFFF;
    for (let i = 0; i < data.length; i++) {
      crc = crcTable[(crc ^ data[i]) & 0xFF] ^ (crc >>> 8);
    }
    return (crc ^ 0xFFFFFFFF) >>> 0;
  }

  generate() {
    const centralDirectory = [];
    let offset = 0;
    const chunks = [];

    // 写入文件数据
    for (const file of this.files) {
      const filename = new TextEncoder().encode(file.filename);
      const fileHeader = new ArrayBuffer(30 + filename.length);
      const view = new DataView(fileHeader);

      // Local file header signature
      view.setUint32(0, 0x04034b50, true);
      // Version needed to extract
      view.setUint16(4, 20, true);
      // General purpose bit flag
      view.setUint16(6, 0, true);
      // Compression method (0 = no compression)
      view.setUint16(8, 0, true);
      // File last modification time & date
      view.setUint16(10, 0, true);
      view.setUint16(12, 0, true);
      // CRC-32
      view.setUint32(14, file.crc32, true);
      // Compressed size
      view.setUint32(18, file.data.length, true);
      // Uncompressed size
      view.setUint32(22, file.data.length, true);
      // File name length
      view.setUint16(26, filename.length, true);
      // Extra field length
      view.setUint16(28, 0, true);

      // 复制文件名
      new Uint8Array(fileHeader, 30).set(filename);

      chunks.push(new Uint8Array(fileHeader));
      chunks.push(file.data);

      // 记录中央目录信息
      centralDirectory.push({
        filename: filename,
        crc32: file.crc32,
        size: file.data.length,
        offset: offset
      });

      offset += fileHeader.byteLength + file.data.length;
    }

    // 写入中央目录
    const centralDirStart = offset;
    for (const entry of centralDirectory) {
      const cdHeader = new ArrayBuffer(46 + entry.filename.length);
      const view = new DataView(cdHeader);

      // Central directory file header signature
      view.setUint32(0, 0x02014b50, true);
      // Version made by
      view.setUint16(4, 20, true);
      // Version needed to extract
      view.setUint16(6, 20, true);
      // General purpose bit flag
      view.setUint16(8, 0, true);
      // Compression method
      view.setUint16(10, 0, true);
      // File last modification time & date
      view.setUint16(12, 0, true);
      view.setUint16(14, 0, true);
      // CRC-32
      view.setUint32(16, entry.crc32, true);
      // Compressed size
      view.setUint32(20, entry.size, true);
      // Uncompressed size
      view.setUint32(24, entry.size, true);
      // File name length
      view.setUint16(28, entry.filename.length, true);
      // Extra field length
      view.setUint16(30, 0, true);
      // File comment length
      view.setUint16(32, 0, true);
      // Disk number start
      view.setUint16(34, 0, true);
      // Internal file attributes
      view.setUint16(36, 0, true);
      // External file attributes
      view.setUint32(38, 0, true);
      // Relative offset of local header
      view.setUint32(42, entry.offset, true);

      // 复制文件名
      new Uint8Array(cdHeader, 46).set(entry.filename);
      chunks.push(new Uint8Array(cdHeader));
      offset += cdHeader.byteLength;
    }

    // 写入中央目录结束记录
    const eocdHeader = new ArrayBuffer(22);
    const eocdView = new DataView(eocdHeader);

    // End of central directory signature
    eocdView.setUint32(0, 0x06054b50, true);
    // Number of this disk
    eocdView.setUint16(4, 0, true);
    // Disk where central directory starts
    eocdView.setUint16(6, 0, true);
    // Number of central directory records on this disk
    eocdView.setUint16(8, this.files.length, true);
    // Total number of central directory records
    eocdView.setUint16(10, this.files.length, true);
    // Size of central directory
    eocdView.setUint32(12, offset - centralDirStart, true);
    // Offset of start of central directory
    eocdView.setUint32(16, centralDirStart, true);
    // Comment length
    eocdView.setUint16(20, 0, true);

    chunks.push(new Uint8Array(eocdHeader));

    // 合并所有数据块
    const totalLength = chunks.reduce((sum, chunk) => sum + chunk.length, 0);
    const result = new Uint8Array(totalLength);
    let pos = 0;
    for (const chunk of chunks) {
      result.set(chunk, pos);
      pos += chunk.length;
    }

    return result;
  }
}

/**
 * 处理文件上传请求
 * @param {Request} request - HTTP请求对象
 * @param {Object} env - 环境变量，包含KV存储
 * @returns {Promise<Response>} - HTTP响应
 */
export async function handleUpload(request, env) {
  const filerKV = env.filer;
  const formData = await request.formData();
  const password = formData.get('password');
  const message = formData.get('message');
  const onetime = formData.get('onetime') === 'true';
  const files = formData.getAll('file').filter(f => f.size > 0);
  const ttlInput = parseInt(formData.get('ttl'), 10);
  const allowedTtls = [3600, 21600, 86400, 604800];
  const expirationTtl = allowedTtls.includes(ttlInput) ? ttlInput : 86400;

  if (!password || (!message && files.length === 0)) {
    return new Response('密码为必填项，且留言和文件至少需要提供一个。', { status: 400 });
  }

  const totalSize = files.reduce((acc, file) => acc + file.size, 0);
  if (totalSize > 25 * 1024 * 1024) {
    return new Response('所有文件总大小不能超过 25MB。', { status: 400 });
  }

  const key = await hashPassword(password);
  
  const filesMetadata = [];
  const putOperations = [];

  for (const file of files) {
    const fileId = crypto.randomUUID();
    filesMetadata.push({ id: fileId, name: file.name, type: file.type, size: file.size });
    putOperations.push(
      filerKV.put(`file:${fileId}`, await file.arrayBuffer(), {
        expirationTtl: expirationTtl,
        metadata: { filename: file.name, contentType: file.type },
      })
    );
  }

  const mainMetadata = {
    message: message || '',
    files: filesMetadata,
    uploadTime: new Date().toISOString(),
    onetime: onetime
  };
  putOperations.push(filerKV.put(`meta:${key}`, JSON.stringify(mainMetadata), { expirationTtl: expirationTtl }));
  await Promise.all(putOperations);

  const { origin } = new URL(request.url);
  const retrievalUrl = `${origin}/retrieve#${encodeURIComponent(password)}`;

  let qrCodeDataUrl = null;
  try {
    const qrApiUrl = `https://api.qrserver.com/v1/create-qr-code/?size=180x180&data=${encodeURIComponent(retrievalUrl)}`;
    const qrResponse = await fetch(qrApiUrl);
    if (qrResponse.ok) {
      const contentType = qrResponse.headers.get('content-type') || 'image/png';
      const imageBuffer = await qrResponse.arrayBuffer();
      const base64String = btoa(String.fromCharCode(...new Uint8Array(imageBuffer)));
      qrCodeDataUrl = `data:${contentType};base64,${base64String}`;
    }
  } catch (e) { console.error("QR Code generation failed:", e); }
  
  return new Response(getSuccessPage(password, qrCodeDataUrl), { headers: { 'Content-Type': 'text/html;charset=UTF-8' } });
}

/**
 * 处理内容获取请求
 * @param {Request} request - HTTP请求对象
 * @param {Object} env - 环境变量，包含KV存储
 * @param {Object} ctx - 执行上下文
 * @returns {Promise<Response>} - HTTP响应
 */
export async function handleFetch(request, env, ctx) {
  const filerKV = env.filer;
  const formData = await request.formData();
  const password = formData.get('password');
  if (!password) return new Response('请输入密码。', { status: 400 });

  const key = await hashPassword(password);
  const meta_key = `meta:${key}`;
  const metadataStr = await filerKV.get(meta_key);

  if (!metadataStr) {
    return new Response(getErrorPage('未找到匹配项。请检查您的密码是否正确，或内容是否已过期。'), {
      status: 404, headers: { 'Content-Type': 'text/html;charset=UTF-8' }
    });
  }

  const metadata = JSON.parse(metadataStr);

  if (metadata.onetime) {
    // 阅后即焚模式：立即删除所有数据
    const deleteOperations = [filerKV.delete(meta_key)];
    if (metadata.files && metadata.files.length > 0) {
      for (const fileInfo of metadata.files) {
        deleteOperations.push(filerKV.delete(`file:${fileInfo.id}`));
      }
    }
    // 等待删除操作完成，确保数据被彻底销毁
    await Promise.all(deleteOperations);
  } else {
    const ttl_5_minutes = 300;
    const updateOperations = [filerKV.put(meta_key, metadataStr, { expirationTtl: ttl_5_minutes })];

    if (metadata.files && metadata.files.length > 0) {
      for (const fileInfo of metadata.files) {
        const file_key = `file:${fileInfo.id}`;
        const fileData = await filerKV.getWithMetadata(file_key, 'arrayBuffer');
        if (fileData.value) {
          updateOperations.push(
            filerKV.put(file_key, fileData.value, { expirationTtl: ttl_5_minutes, metadata: fileData.metadata })
          );
        }
      }
    }
    await Promise.all(updateOperations);
  }

  return new Response(getRetrieveResultPage(metadata, key), { headers: { 'Content-Type': 'text/html;charset=UTF-8' } });
}

/**
 * 处理文件下载请求
 * @param {Request} request - HTTP请求对象（未使用但保持接口一致性）
 * @param {Object} env - 环境变量，包含KV存储
 * @param {Object} ctx - 执行上下文（未使用但保持接口一致性）
 * @param {Object} params - 路由参数，包含fileId
 * @returns {Promise<Response>} - HTTP响应
 */
export async function handleDownload(request, env, ctx, params) {
  const { fileId } = params;
  const filerKV = env.filer;
  if (!fileId) return new Response('无效的下载链接', { status: 400 });
  const { value, metadata } = await filerKV.getWithMetadata(`file:${fileId}`, 'arrayBuffer');
  if (!value || !metadata) return new Response('文件未找到或已过期。', { status: 404 });
  return new Response(value, {
    headers: {
      'Content-Type': metadata.contentType || 'application/octet-stream',
      'Content-Disposition': `attachment; filename*=UTF-8''${encodeURIComponent(metadata.filename || 'download')}`,
    }
  });
}

/**
 * 处理文件预览请求
 * @param {Request} request - HTTP请求对象
 * @param {Object} env - 环境变量，包含KV存储
 * @param {Object} ctx - 执行上下文
 * @param {Object} params - 路由参数，包含fileId
 * @returns {Promise<Response>} - HTTP响应
 */
export async function handlePreview(request, env, ctx, params) {
  const { fileId } = params;
  const filerKV = env.filer;
  if (!fileId) return new Response('无效的预览链接', { status: 400 });

  const { value, metadata } = await filerKV.getWithMetadata(`file:${fileId}`, 'arrayBuffer');
  if (!value || !metadata) return new Response('文件未找到或已过期。', { status: 404 });

  const contentType = metadata.contentType || 'application/octet-stream';

  // 设置适当的缓存头和内容类型，用于在线预览
  return new Response(value, {
    headers: {
      'Content-Type': contentType,
      'Cache-Control': 'public, max-age=300', // 5分钟缓存
      'Content-Disposition': `inline; filename*=UTF-8''${encodeURIComponent(metadata.filename || 'preview')}`,
    }
  });
}

/**
 * 处理打包下载请求
 * @param {Request} request - HTTP请求对象
 * @param {Object} env - 环境变量，包含KV存储
 * @param {Object} ctx - 执行上下文
 * @param {Object} params - 路由参数，包含password hash
 * @returns {Promise<Response>} - HTTP响应
 */
export async function handlePackageDownload(request, env, ctx, params) {
  const { passwordHash } = params;
  const filerKV = env.filer;

  if (!passwordHash) return new Response('无效的打包下载链接', { status: 400 });

  const meta_key = `meta:${passwordHash}`;
  const metadataStr = await filerKV.get(meta_key);

  if (!metadataStr) {
    return new Response('未找到匹配项或内容已过期。', { status: 404 });
  }

  const metadata = JSON.parse(metadataStr);

  if (!metadata.files || metadata.files.length === 0) {
    return new Response('没有文件可以打包下载。', { status: 400 });
  }

  // 如果是阅后即焚模式，这个链接不应该存在（因为在提取时就已经删除了）
  // 但为了安全起见，我们再次检查并删除
  if (metadata.onetime) {
    const deleteOperations = [filerKV.delete(meta_key)];
    if (metadata.files && metadata.files.length > 0) {
      for (const fileInfo of metadata.files) {
        deleteOperations.push(filerKV.delete(`file:${fileInfo.id}`));
      }
    }
    await Promise.all(deleteOperations);
    return new Response('此内容为阅后即焚模式，已被销毁。', { status: 410 });
  }

  try {
    const zip = new SimpleZip();

    // 获取所有文件并添加到ZIP
    const filePromises = metadata.files.map(async (fileInfo) => {
      const file_key = `file:${fileInfo.id}`;
      const { value, metadata: fileMeta } = await filerKV.getWithMetadata(file_key, 'arrayBuffer');

      if (value) {
        zip.addFile(fileInfo.name, value);
        return { success: true, name: fileInfo.name, size: value.byteLength };
      } else {
        return { success: false, name: fileInfo.name, error: 'File not found' };
      }
    });

    const results = await Promise.all(filePromises);
    const successCount = results.filter(r => r.success).length;

    if (successCount === 0) {
      return new Response('没有找到可下载的文件。', { status: 404 });
    }

    // 生成ZIP文件
    const zipData = zip.generate();

    // 生成文件名（使用上传时间或当前时间）
    const uploadTime = metadata.uploadTime ? new Date(metadata.uploadTime) : new Date();
    const dateStr = uploadTime.toISOString().slice(0, 10).replace(/-/g, '');
    const timeStr = uploadTime.toISOString().slice(11, 19).replace(/:/g, '');
    const zipFileName = `files_${dateStr}_${timeStr}.zip`;

    return new Response(zipData, {
      headers: {
        'Content-Type': 'application/zip',
        'Content-Disposition': `attachment; filename*=UTF-8''${encodeURIComponent(zipFileName)}`,
        'Content-Length': zipData.length.toString(),
      }
    });

  } catch (error) {
    console.error('打包下载失败:', error);
    return new Response('打包下载失败，请稍后重试。', { status: 500 });
  }
}
